{"name": "omo-dev", "private": true, "scripts": {"build": "remix vite:build", "dev": "shopify app dev", "config:link": "shopify app config link", "generate": "shopify app generate", "deploy": "shopify app deploy", "config:use": "shopify app config use", "env": "shopify app env", "start": "remix-serve ./build/server/index.js", "docker-start": "npm run setup && npm run start", "setup": "prisma generate && prisma migrate deploy", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "shopify": "shopify", "prisma": "prisma", "graphql-codegen": "graphql-codegen", "vite": "vite"}, "type": "module", "engines": {"node": "^18.20 || ^20.10 || >=21.0.0"}, "dependencies": {"@apollo/client": "^3.12.11", "@prisma/client": "^6.10.1", "@remix-run/css-bundle": "^2.16.4", "@remix-run/dev": "^2.16.4", "@remix-run/fs-routes": "^2.16.4", "@remix-run/node": "^2.16.4", "@remix-run/react": "^2.16.4", "@remix-run/serve": "^2.16.4", "@shopify/app-bridge-react": "^4.1.6", "@shopify/polaris": "^13.9.5", "@shopify/polaris-icons": "^9.3.1", "@shopify/shopify-app-remix": "^3.8.2", "@shopify/shopify-app-session-storage-prisma": "^6.0.0", "@tailwindcss/vite": "^4.0.9", "bullmq": "^5.56.10", "dayjs": "^1.11.13", "graphql": "^16.11.0", "graphql-tools": "^9.0.18", "i18next": "^23.15.1", "i18next-browser-languagedetector": "^8.0.0", "i18next-fs-backend": "^2.3.2", "i18next-http-backend": "^2.6.1", "ioredis": "^5.7.0", "isbot": "^5.1.0", "isomorphic-dompurify": "^2.25.0", "joi": "^17.13.3", "lucide-react": "^0.476.0", "node-cron": "^4.0.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^13.0.0", "remix-i18next": "^6.4.1", "semaphore": "^1.1.0", "vite-tsconfig-paths": "^5.0.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.24.2"}, "devDependencies": {"@remix-run/eslint-config": "^2.16.4", "@remix-run/route-config": "^2.16.4", "@shopify/api-codegen-preset": "^1.1.1", "@types/eslint": "^9.6.1", "@types/node": "^22.2.0", "@types/react": "^18.2.31", "@types/react-dom": "^18.2.14", "@types/semaphore": "^1.1.4", "autoprefixer": "^10.4.20", "eslint": "^8.42.0", "eslint-config-prettier": "^10.0.1", "postcss": "^8.5.3", "prettier": "^3.2.4", "prisma": "^6.10.1", "tailwindcss": "^3.4.17", "typescript": "^5.2.2", "vite": "^5.1.7"}, "workspaces": ["extensions/*"], "trustedDependencies": ["@shopify/plugin-cloudflare"], "resolutions": {"@graphql-tools/url-loader": "8.0.16"}, "overrides": {"@graphql-tools/url-loader": "8.0.16"}, "author": "quoc<PERSON>c", "prisma": {"seed": "node prisma/seed.cjs"}}