version: '3.8'

networks:
  traefik:
    external: true
  internal:
    driver: bridge

volumes:
  mysql_data_prod:
  redis_data_prod:
  minio_data_prod:
  loki_index_prod:
  loki_index_cache_prod:
  grafana_data_prod:
  traefik_data:

services:
  # Traefik Reverse Proxy
  traefik:
    image: traefik:v3.0
    container_name: traefik
    restart: unless-stopped
    command:
      - --api.dashboard=true
      - --api.debug=true
      - --log.level=INFO
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --providers.docker.network=traefik
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --certificatesresolvers.letsencrypt.acme.tlschallenge=true
      - --certificatesresolvers.letsencrypt.acme.email=${ACME_EMAIL}
      - --certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json
      - --metrics.prometheus=true
      - --accesslog=true
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik_data:/letsencrypt
    networks:
      - traefik
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`traefik.${DOMAIN}`)"
      - "traefik.http.routers.traefik.entrypoints=websecure"
      - "traefik.http.routers.traefik.tls.certresolver=letsencrypt"
      - "traefik.http.routers.traefik.service=api@internal"
      - "traefik.http.routers.traefik.middlewares=auth"
      - "traefik.http.middlewares.auth.basicauth.users=${TRAEFIK_AUTH}"
      # Global redirect to https
      - "traefik.http.routers.http-catchall.rule=hostregexp(`{host:.+}`)"
      - "traefik.http.routers.http-catchall.entrypoints=web"
      - "traefik.http.routers.http-catchall.middlewares=redirect-to-https"
      - "traefik.http.middlewares.redirect-to-https.redirectscheme.scheme=https"

  # MySQL Database
  mysql:
    image: mysql:8.4
    container_name: omo_mysql_prod
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql_data_prod:/var/lib/mysql
      - ./omo-shopify-app/init.sql:/docker-entrypoint-initdb.d
    networks:
      - internal
    command: >
      --default-authentication-plugin=mysql_native_password
      --innodb-buffer-pool-size=1G
      --innodb-log-file-size=256M
      --max-connections=200
      --query-cache-type=1
      --query-cache-size=64M
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${DB_ROOT_PASSWORD}"]
      timeout: 10s
      retries: 10
      interval: 30s

  # Redis Cache & Queue
  redis:
    image: redis:7-alpine
    container_name: omo_redis_prod
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data_prod:/data
    networks:
      - internal
    healthcheck:
      test: ["CMD-SHELL", "redis-cli ping | grep PONG"]
      interval: 10s
      timeout: 3s
      retries: 5

  # MinIO Object Storage
  minio:
    image: quay.io/minio/minio:latest
    container_name: omo_minio_prod
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    volumes:
      - minio_data_prod:/data
    networks:
      - internal
      - traefik
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.minio-api.rule=Host(`storage.${DOMAIN}`)"
      - "traefik.http.routers.minio-api.entrypoints=websecure"
      - "traefik.http.routers.minio-api.tls.certresolver=letsencrypt"
      - "traefik.http.routers.minio-api.service=minio-api"
      - "traefik.http.services.minio-api.loadbalancer.server.port=9000"
      - "traefik.http.routers.minio-console.rule=Host(`minio.${DOMAIN}`)"
      - "traefik.http.routers.minio-console.entrypoints=websecure"
      - "traefik.http.routers.minio-console.tls.certresolver=letsencrypt"
      - "traefik.http.routers.minio-console.service=minio-console"
      - "traefik.http.services.minio-console.loadbalancer.server.port=9001"

  # MinIO Bucket Initialization
  minio-mc-init:
    image: minio/mc:latest
    depends_on:
      - minio
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    networks:
      - internal
    entrypoint: >
      /bin/sh -c "
      until (mc alias set local http://minio:9000 $$MINIO_ROOT_USER $$MINIO_ROOT_PASSWORD) do sleep 1; done &&
      mc mb -p local/loki || true &&
      mc mb -p local/backups || true
      "
    restart: "no"

  # Loki Log Aggregation
  loki:
    image: grafana/loki:latest
    container_name: omo_loki_prod
    restart: unless-stopped
    depends_on:
      - minio
      - minio-mc-init
    command: ["-config.file=/etc/loki/loki-config.yml"]
    environment:
      LOKI_S3_ENDPOINT: http://minio:9000
      LOKI_S3_BUCKET: loki
      LOKI_S3_REGION: us-east-1
      AWS_ACCESS_KEY_ID: ${MINIO_ROOT_USER}
      AWS_SECRET_ACCESS_KEY: ${MINIO_ROOT_PASSWORD}
    volumes:
      - loki_index_prod:/loki/index
      - loki_index_cache_prod:/loki/index_cache
      - ./loki-config.yml:/etc/loki/loki-config.yml:ro
    networks:
      - internal
    user: "10001"

  # Grafana Monitoring
  grafana:
    image: grafana/grafana:latest
    container_name: omo_grafana_prod
    restart: unless-stopped
    depends_on:
      - loki
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_SERVER_ROOT_URL: https://monitoring.${DOMAIN}
      GF_SECURITY_COOKIE_SECURE: "true"
      GF_SECURITY_STRICT_TRANSPORT_SECURITY: "true"
    volumes:
      - grafana_data_prod:/var/lib/grafana
      - ./provisioning/datasources/datasource.yml:/etc/grafana/provisioning/datasources/datasource.yml:ro
    networks:
      - internal
      - traefik
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=Host(`monitoring.${DOMAIN}`)"
      - "traefik.http.routers.grafana.entrypoints=websecure"
      - "traefik.http.routers.grafana.tls.certresolver=letsencrypt"
      - "traefik.http.services.grafana.loadbalancer.server.port=3000"

  # OMO Shopify App
  omo-shopify-app:
    build:
      context: ./omo-shopify-app
      dockerfile: Dockerfile
    container_name: omo_shopify_app_prod
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      NODE_ENV: production
      DATABASE_URL: mysql://${DB_USER}:${DB_PASSWORD}@mysql:3306/${DB_NAME}
      SHOPIFY_API_KEY: ${SHOPIFY_API_KEY}
      SHOPIFY_API_SECRET: ${SHOPIFY_API_SECRET}
      SHOPIFY_APP_URL: https://app.${DOMAIN}
      SCOPES: ${SHOPIFY_SCOPES}
      SYSTEM_TOKEN: ${SYSTEM_TOKEN}
      API_VERSION: ${SHOPIFY_API_VERSION}
      PORT: 3000
    volumes:
      - ./omo-shopify-app/logs:/app/logs
    networks:
      - internal
      - traefik
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.shopify-app.rule=Host(`app.${DOMAIN}`)"
      - "traefik.http.routers.shopify-app.entrypoints=websecure"
      - "traefik.http.routers.shopify-app.tls.certresolver=letsencrypt"
      - "traefik.http.services.shopify-app.loadbalancer.server.port=3000"
      - "traefik.http.routers.shopify-app.middlewares=security-headers"
      - "traefik.http.middlewares.security-headers.headers.customrequestheaders.X-Forwarded-Proto=https"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # OMO NestJS Backend
  omo-nest-be:
    build:
      context: ./omo-nest-be
      dockerfile: Dockerfile
    container_name: omo_nest_be_prod
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      NODE_ENV: production
      DATABASE_URL: mysql://${DB_USER}:${DB_PASSWORD}@mysql:3306/${DB_NAME}
      REDIS_URL: redis://redis:6379
      PORT: 3000
    networks:
      - internal
      - traefik
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.nest-api.rule=Host(`api.${DOMAIN}`)"
      - "traefik.http.routers.nest-api.entrypoints=websecure"
      - "traefik.http.routers.nest-api.tls.certresolver=letsencrypt"
      - "traefik.http.services.nest-api.loadbalancer.server.port=3000"
      - "traefik.http.routers.nest-api.middlewares=api-cors"
      - "traefik.http.middlewares.api-cors.headers.accesscontrolallowmethods=GET,OPTIONS,PUT,POST,DELETE,PATCH"
      - "traefik.http.middlewares.api-cors.headers.accesscontrolalloworiginlist=https://app.${DOMAIN}"
      - "traefik.http.middlewares.api-cors.headers.accesscontrolmaxage=100"
      - "traefik.http.middlewares.api-cors.headers.addvaryheader=true"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
