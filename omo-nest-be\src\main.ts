import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { ExpressAdapter } from '@bull-board/express';
import { NestFactory } from '@nestjs/core';
import { Queue } from 'bullmq';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import { AppModule } from './app.module';
import { QueueName } from './queue/constant';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Enable CORS for development
  app.enableCors({
    origin: true,
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  });

  app.useLogger(app.get(WINSTON_MODULE_NEST_PROVIDER));
  // Set global prefix
  app.setGlobalPrefix('api');

  const serverAdapter = new ExpressAdapter();
  serverAdapter.setBasePath('/admin/queues');

  createBullBoard({
    queues: [
      new BullMQAdapter(new Queue('example')),
      new BullMQAdapter(new Queue(QueueName.CUSTOMERS_CREATE)),
      new BullMQAdapter(new Queue(QueueName.CUSTOMER_TIMELINE)),
      new BullMQAdapter(new Queue(QueueName.ACLAS_MEMBER_ADD)),
      new BullMQAdapter(new Queue(QueueName.ACLAS_TRANSACTIONS)),
    ],
    serverAdapter,
  });

  app.use('/admin/queues', serverAdapter.getRouter());

  const port = process.env.PORT || 3000;
  await app.listen(port);

  console.log('Application is running on:', `http://localhost:${port}`);
  console.log(
    'Bull Board UI is available at:',
    `http://localhost:${port}/admin/queues`,
  );
}

bootstrap().catch((error) => {
  console.error('Failed to start application:', error);
  process.exit(1);
});
