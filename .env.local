# ==============================================
# OMO System Local Testing Environment Variables
# ==============================================

# Local Domain Configuration (using .localhost domains)
DOMAIN=localhost
ACME_EMAIL=test@localhost

# Database Configuration
DB_ROOT_PASSWORD=local_root_password
DB_NAME=omo_local
DB_USER=omo_local_user
DB_PASSWORD=local_db_password
DB_HOST=mysql
DB_PORT=3306

# Shopify App Configuration (use your development app credentials)
SHOPIFY_API_KEY=your_dev_shopify_api_key
SHOPIFY_API_SECRET=your_dev_shopify_api_secret
SHOPIFY_SCOPES=write_products,write_orders,read_customers,write_customers,write_discounts,write_draft_orders,read_returns,write_returns
SHOPIFY_API_VERSION=2025-01

# System Configuration
SYSTEM_TOKEN=local_system_token_for_testing

# MinIO Configuration
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minio123456

# Grafana Configuration
GRAFANA_USER=admin
GRAFANA_PASSWORD=admin123

# Traefik Authentication (not needed for local testing)
TRAEFIK_AUTH=admin:$$2y$$10$$test

# Redis Configuration
REDIS_PASSWORD=

# Logging Configuration
LOG_LEVEL=debug

# Backup Configuration (not used in local)
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=7

# SSL Configuration (disabled for local)
SSL_EMAIL=test@localhost

# Security Configuration (relaxed for local testing)
SECURE_COOKIES=false
FORCE_HTTPS=false

# Performance Configuration (reduced for local)
MAX_MEMORY_MYSQL=512M
MAX_MEMORY_REDIS=256M

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090

# Local Testing Specific
NODE_ENV=development
